# Yoghurt AI QC - 数据库架构图

## 完整的实体关系图

```mermaid
erDiagram
    User ||--o{ Recipe : creates
    User ||--o{ Batch : manages
    User ||--o{ SensoryAssessment : evaluates
    User ||--o{ MicroscopeImage : uploads
    User ||--o{ QualityReport : generates
    User ||--o{ SystemLog : performs
    
    Recipe ||--o{ Batch : uses
    Batch ||--o{ SensoryAssessment : has
    Batch ||--o{ MicroscopeImage : contains
    Batch ||--o{ AIAnalysis : analyzes
    Batch ||--o{ QualityReport : produces
    
    MicroscopeImage ||--o{ AIAnalysis : generates
    
    User {
        uuid id PK
        string email UK "唯一邮箱"
        string password_hash "密码哈希"
        string name "用户姓名"
        enum role "ADMIN|USER|VIEWER"
        boolean is_active "账户状态"
        timestamp created_at
        timestamp updated_at
    }
    
    Recipe {
        uuid id PK
        uuid user_id FK
        string name "配方名称"
        text description "配方描述"
        jsonb ingredients "配料列表"
        jsonb process "制作步骤"
        decimal fermentation_temperature "发酵温度"
        integer fermentation_duration "发酵时长(小时)"
        integer filtration_duration "过滤时长(小时)"
        integer version "版本号"
        boolean is_active "是否启用"
        timestamp created_at
        timestamp updated_at
    }
    
    Batch {
        uuid id PK
        string batch_number UK "批次号"
        uuid recipe_id FK
        uuid user_id FK
        enum status "PLANNING|IN_PROGRESS|FERMENTING|FILTERING|COMPLETED|FAILED"
        date production_date "生产日期"
        decimal quantity "数量(L或kg)"
        integer actual_fermentation_duration "实际发酵时长"
        decimal actual_temperature "实际温度"
        text notes "备注"
        timestamp created_at
        timestamp updated_at
    }
    
    SensoryAssessment {
        uuid id PK
        uuid batch_id FK
        uuid assessor_id FK
        smallint texture_score "质地评分(1-5)"
        smallint acidity_score "酸度评分(1-5)"
        jsonb flavor_tags "风味标签"
        text flavor_notes "风味备注"
        smallint overall_score "综合评分(1-5)"
        text notes "评估备注"
        timestamp created_at
        timestamp updated_at
    }
    
    MicroscopeImage {
        uuid id PK
        uuid batch_id FK
        string filename "文件名"
        string original_name "原始文件名"
        string file_path "文件路径"
        integer file_size "文件大小(bytes)"
        string mime_type "MIME类型"
        integer magnification "放大倍数"
        string staining_method "染色方法"
        integer image_width "图像宽度"
        integer image_height "图像高度"
        jsonb metadata "图像元数据"
        uuid uploaded_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    AIAnalysis {
        uuid id PK
        uuid batch_id FK
        uuid microscope_image_id FK
        enum analysis_status "PENDING|PROCESSING|COMPLETED|FAILED|CANCELLED"
        decimal quality_score "质量评分(0-100)"
        integer bacterial_count "细菌计数"
        boolean contamination_detected "是否检测到污染"
        string contamination_type "污染类型"
        jsonb morphology_analysis "形态学分析"
        decimal confidence_score "置信度(0-100)"
        text summary_recommendation "总结和建议"
        integer processing_time_ms "处理时间(毫秒)"
        string model_version "模型版本"
        string api_provider "API提供商"
        jsonb raw_response "原始AI响应"
        text error_message "错误信息"
        integer retry_count "重试次数"
        timestamp created_at
        timestamp updated_at
    }
    
    QualityReport {
        uuid id PK
        uuid batch_id FK
        enum report_type "QUALITY_SUMMARY|BATCH_ANALYSIS|TREND_ANALYSIS|CONTAMINATION_REPORT|CUSTOM"
        string title "报告标题"
        text description "报告描述"
        jsonb date_range "日期范围"
        jsonb filters "筛选条件"
        jsonb report_data "报告数据"
        uuid generated_by FK
        boolean is_public "是否公开"
        timestamp created_at
        timestamp updated_at
    }
    
    SystemLog {
        uuid id PK
        uuid user_id FK
        string action "操作类型"
        string resource_type "资源类型"
        uuid resource_id "资源ID"
        jsonb details "详细信息"
        inet ip_address "IP地址"
        text user_agent "用户代理"
        timestamp created_at
    }
```

## 数据流程图

```mermaid
flowchart TD
    A[用户登录] --> B[创建配方]
    B --> C[开始新批次]
    C --> D[生产酸奶]
    D --> E[感官评估]
    D --> F[显微镜拍照]
    F --> G[上传图像]
    G --> H[AI分析]
    H --> I[生成分析报告]
    E --> J[记录评估结果]
    I --> K[质量报告]
    J --> K
    K --> L[数据看板]
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style K fill:#f3e5f5
    style L fill:#e8f5e8
```

## 索引设计图

```mermaid
graph TD
    subgraph "用户表索引"
        U1[email - 唯一索引]
        U2[role - 普通索引]
        U3[is_active - 普通索引]
        U4[created_at - 普通索引]
    end
    
    subgraph "批次表索引"
        B1[batch_number - 唯一索引]
        B2[recipe_id - 外键索引]
        B3[user_id - 外键索引]
        B4[status - 普通索引]
        B5[production_date - 普通索引]
        B6[user_id + status - 组合索引]
        B7[recipe_id + status - 组合索引]
    end
    
    subgraph "AI分析表索引"
        A1[batch_id - 外键索引]
        A2[analysis_status - 普通索引]
        A3[quality_score - 普通索引]
        A4[contamination_detected - 普通索引]
        A5[batch_id + analysis_status - 组合索引]
    end
    
    style U1 fill:#ffcdd2
    style B1 fill:#ffcdd2
    style B6 fill:#c8e6c9
    style B7 fill:#c8e6c9
    style A5 fill:#c8e6c9
```

## 数据类型说明

### 枚举类型

```sql
-- 用户角色
CREATE TYPE user_role AS ENUM ('ADMIN', 'USER', 'VIEWER');

-- 批次状态
CREATE TYPE batch_status AS ENUM (
    'PLANNING', 'IN_PROGRESS', 'FERMENTING', 
    'FILTERING', 'COMPLETED', 'FAILED'
);

-- 分析状态
CREATE TYPE analysis_status AS ENUM (
    'PENDING', 'PROCESSING', 'COMPLETED', 
    'FAILED', 'CANCELLED'
);

-- 报告类型
CREATE TYPE report_type AS ENUM (
    'QUALITY_SUMMARY', 'BATCH_ANALYSIS', 
    'TREND_ANALYSIS', 'CONTAMINATION_REPORT', 'CUSTOM'
);
```

### JSONB 字段结构

#### 配方配料 (Recipe.ingredients)
```json
[
  {
    "name": "全脂牛奶",
    "amount": 1000,
    "unit": "ml"
  },
  {
    "name": "酸奶菌粉", 
    "amount": 1,
    "unit": "包"
  }
]
```

#### 制作步骤 (Recipe.process)
```json
[
  {
    "step": 1,
    "description": "牛奶加热至85°C杀菌",
    "duration": 30,
    "temperature": 85
  },
  {
    "step": 2,
    "description": "冷却至43°C",
    "duration": 60,
    "temperature": 43
  }
]
```

#### 形态学分析 (AIAnalysis.morphology_analysis)
```json
{
  "bacterial_morphology": "清晰可见杆状乳酸菌和链状球菌",
  "balance_ratio": "乳酸杆菌与链球菌比例约为 1.2:1",
  "activity_level": "菌体饱满，分布均匀，活性良好"
}
```

## 性能考虑

### 查询优化
1. **分页查询**: 使用 LIMIT 和 OFFSET
2. **索引覆盖**: 常用查询字段建立组合索引
3. **JSONB查询**: 对频繁查询的JSONB字段考虑GIN索引

### 存储优化
1. **UUID vs 自增ID**: UUID保证全局唯一，便于分布式
2. **JSONB vs 关系表**: 灵活性vs查询性能的权衡
3. **文件存储**: 图像文件存储在文件系统，数据库仅存路径

### 扩展性考虑
1. **水平分片**: 按用户或时间分片
2. **读写分离**: 主从复制提高读性能
3. **缓存策略**: Redis缓存热点数据
<svg aria-roledescription="er" role="graphics-document document" viewBox="0 0 1151.1241455078125 2043.6805419921875" style="max-width: 1151.1241455078125px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927"><style>#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .error-icon{fill:#a44141;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .edge-thickness-normal{stroke-width:1px;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .marker.cross{stroke:lightgrey;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 p{margin:0;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .entityBox{fill:#1f2020;stroke:#ccc;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .attributeBoxOdd{fill:hsl(0, 0%, 32%);stroke:#ccc;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .attributeBoxEven{fill:hsl(0, 0%, 22%);stroke:#ccc;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .relationshipLabelBox{fill:hsl(20, 1.5873015873%, 12.3529411765%);opacity:0.7;background-color:hsl(20, 1.5873015873%, 12.3529411765%);}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .relationshipLabelBox rect{opacity:0.5;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .relationshipLine{stroke:lightgrey;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 .entityTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 #MD_PARENT_START{fill:#f5f5f5!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 #MD_PARENT_END{fill:#f5f5f5!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-875bcaff-2415-4f3a-99c3-b0ccba5b4927 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" id="MD_PARENT_START"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" id="MD_PARENT_END"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="0" id="ONLY_ONE_START"><path d="M9,0 L9,18 M15,0 L15,18" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="18" id="ONLY_ONE_END"><path d="M3,0 L3,18 M9,0 L9,18" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="0" id="ZERO_OR_ONE_START"><circle r="6" cy="9" cx="21" fill="white" stroke="gray"></circle><path d="M9,0 L9,18" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="30" id="ZERO_OR_ONE_END"><circle r="6" cy="9" cx="9" fill="white" stroke="gray"></circle><path d="M21,0 L21,18" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="18" id="ONE_OR_MORE_START"><path d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="27" id="ONE_OR_MORE_END"><path d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="18" id="ZERO_OR_MORE_START"><circle r="6" cy="18" cx="48" fill="white" stroke="gray"></circle><path d="M0,18 Q18,0 36,18 Q18,36 0,18" fill="none" stroke="gray"></path></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="39" id="ZERO_OR_MORE_END"><circle r="6" cy="18" cx="9" fill="white" stroke="gray"></circle><path d="M21,18 Q39,0 57,18 Q39,36 21,18" fill="none" stroke="gray"></path></marker></defs><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M552.93,158.691L592.018,177.428C631.106,196.165,709.283,233.638,748.371,260.708C787.459,287.778,787.459,304.444,787.459,312.778L787.459,321.111" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M545.018,221.111L550.955,229.444C556.893,237.778,568.768,254.444,574.706,297.101C580.643,339.757,580.643,408.403,580.643,477.049C580.643,545.694,580.643,614.34,585.065,656.997C589.488,699.653,598.332,716.319,602.755,724.653L607.177,732.986" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M393.812,148.635L335.976,169.048C278.141,189.461,162.469,230.286,104.633,285.021C46.797,339.757,46.797,408.403,46.797,477.049C46.797,545.694,46.797,614.34,46.797,681.14C46.797,747.94,46.797,812.894,46.797,877.847C46.797,942.801,46.797,1007.755,50.243,1055.949C53.688,1104.144,60.579,1135.579,64.025,1151.296L67.47,1167.014" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M552.93,143.381L627.131,164.669C701.333,185.958,849.736,228.534,923.938,284.146C998.139,339.757,998.139,408.403,998.139,477.049C998.139,545.694,998.139,614.34,998.139,681.14C998.139,747.94,998.139,812.894,998.139,877.847C998.139,942.801,998.139,1007.755,999.966,1048.565C1001.793,1089.375,1005.446,1106.042,1007.273,1114.375L1009.1,1122.708" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M439.976,221.111L437.209,229.444C434.441,237.778,428.906,254.444,426.139,297.101C423.371,339.757,423.371,408.403,423.371,477.049C423.371,545.694,423.371,614.34,423.371,681.14C423.371,747.94,423.371,812.894,423.371,877.847C423.371,942.801,423.371,1007.755,436.214,1059.327C449.057,1110.899,474.743,1149.089,487.586,1168.185L500.429,1187.28" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M393.812,170.799L367.339,187.518C340.866,204.237,287.919,237.674,261.446,270.11C234.972,302.546,234.972,333.981,234.972,349.699L234.972,365.417" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M787.459,632.986L787.459,641.319C787.459,649.653,787.459,666.319,783.037,682.986C778.614,699.653,769.77,716.319,765.348,724.653L760.925,732.986" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M569.507,919.392L499.055,944.945C428.604,970.498,287.701,1021.603,213.804,1062.873C139.906,1104.144,133.016,1135.579,129.57,1151.296L126.125,1167.014" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M798.595,931.749L848.519,955.243C898.443,978.736,998.291,1025.722,1046.388,1057.549C1094.486,1089.375,1090.832,1106.042,1089.005,1114.375L1087.179,1122.708" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M569.507,933.808L522.122,956.958C474.737,980.108,379.966,1026.408,332.581,1087.573C285.196,1148.738,285.196,1224.769,285.196,1300.799C285.196,1376.829,285.196,1452.859,327.502,1522.824C369.808,1592.79,454.419,1656.691,496.725,1688.641L539.03,1720.592" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M684.051,1022.708L684.051,1031.042C684.051,1039.375,684.051,1056.042,677.527,1078.247C671.003,1100.451,657.956,1128.194,651.432,1142.066L644.908,1155.938" class="er relationshipLine"></path><path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M1048.139,1478.889L1048.139,1487.222C1048.139,1495.556,1048.139,1512.222,998.903,1553.896C949.667,1595.57,851.194,1662.252,801.958,1695.593L752.722,1728.933" class="er relationshipLine"></path><g transform="translate(393.8121223449707,20 )" id="entity-User-818c511a-4a32-5484-ba70-875d765a9175"><rect height="201.11110973358154" width="159.1178331375122" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(79.5589165687561,11.944444179534912)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175" class="er entityLabel">User</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="77.97959899902344" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="22.483723640441895" y="23.888888359069824" x="136.6341094970703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,34.96527719497681)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-2-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="77.97959899902344" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-2-name" class="er entityLabel">email</text><rect height="22.152777671813965" width="22.483723640441895" y="46.04166603088379" x="136.6341094970703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,57.11805486679077)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-2-key" class="er entityLabel">UK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-3-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="77.97959899902344" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-3-name" class="er entityLabel">password_hash</text><rect height="22.152777671813965" width="22.483723640441895" y="68.19444370269775" x="136.6341094970703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,79.27083253860474)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-3-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-4-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="77.97959899902344" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-4-name" class="er entityLabel">name</text><rect height="22.152777671813965" width="22.483723640441895" y="90.34722137451172" x="136.6341094970703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,101.4236102104187)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-5-type" class="er entityLabel">enum</text><rect height="22.152777671813965" width="77.97959899902344" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-5-name" class="er entityLabel">role</text><rect height="22.152777671813965" width="22.483723640441895" y="112.49999904632568" x="136.6341094970703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,123.57638788223267)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-6-type" class="er entityLabel">boolean</text><rect height="22.152777671813965" width="77.97959899902344" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-6-name" class="er entityLabel">is_active</text><rect height="22.152777671813965" width="22.483723640441895" y="134.65277671813965" x="136.6341094970703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,145.72916555404663)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-7-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="77.97959899902344" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-7-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="22.483723640441895" y="156.8055543899536" x="136.6341094970703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,167.8819432258606)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-8-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="77.97959899902344" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-8-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="22.483723640441895" y="178.95833206176758" x="136.6341094970703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.6341094970703,190.03472089767456)" y="0" x="0" id="text-entity-User-818c511a-4a32-5484-ba70-875d765a9175-attr-8-key" class="er entityLabel"></text></g><g transform="translate(680.6431732177734,321.1111145019531 )" id="entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689"><rect height="311.87499809265137" width="213.6317195892334" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(106.8158597946167,11.944444179534912)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689" class="er entityLabel">Recipe</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="133.4157943725586" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="21.56141471862793" y="23.888888359069824" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,34.96527719497681)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-2-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="133.4157943725586" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-2-name" class="er entityLabel">user_id</text><rect height="22.152777671813965" width="21.56141471862793" y="46.04166603088379" x="192.07030487060547" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,57.11805486679077)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-2-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-3-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="133.4157943725586" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-3-name" class="er entityLabel">name</text><rect height="22.152777671813965" width="21.56141471862793" y="68.19444370269775" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,79.27083253860474)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-3-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-4-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="133.4157943725586" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-4-name" class="er entityLabel">description</text><rect height="22.152777671813965" width="21.56141471862793" y="90.34722137451172" x="192.07030487060547" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,101.4236102104187)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-5-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="133.4157943725586" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-5-name" class="er entityLabel">ingredients</text><rect height="22.152777671813965" width="21.56141471862793" y="112.49999904632568" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,123.57638788223267)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-6-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="133.4157943725586" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-6-name" class="er entityLabel">process</text><rect height="22.152777671813965" width="21.56141471862793" y="134.65277671813965" x="192.07030487060547" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,145.72916555404663)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-7-type" class="er entityLabel">decimal</text><rect height="22.152777671813965" width="133.4157943725586" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-7-name" class="er entityLabel">fermentation_temperature</text><rect height="22.152777671813965" width="21.56141471862793" y="156.8055543899536" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,167.8819432258606)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-8-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="133.4157943725586" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-8-name" class="er entityLabel">fermentation_duration</text><rect height="22.152777671813965" width="21.56141471862793" y="178.95833206176758" x="192.07030487060547" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,190.03472089767456)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-9-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="133.4157943725586" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-9-name" class="er entityLabel">filtration_duration</text><rect height="22.152777671813965" width="21.56141471862793" y="201.11110973358154" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,212.18749856948853)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-9-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="223.2638874053955" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,234.3402762413025)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-10-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="133.4157943725586" y="223.2638874053955" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,234.3402762413025)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-10-name" class="er entityLabel">version</text><rect height="22.152777671813965" width="21.56141471862793" y="223.2638874053955" x="192.07030487060547" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,234.3402762413025)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-10-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="245.41666507720947" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,256.49305391311646)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-11-type" class="er entityLabel">boolean</text><rect height="22.152777671813965" width="133.4157943725586" y="245.41666507720947" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,256.49305391311646)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-11-name" class="er entityLabel">is_active</text><rect height="22.152777671813965" width="21.56141471862793" y="245.41666507720947" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,256.49305391311646)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-11-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="267.56944274902344" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,278.6458315849304)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-12-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="133.4157943725586" y="267.56944274902344" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,278.6458315849304)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-12-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="21.56141471862793" y="267.56944274902344" x="192.07030487060547" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,278.6458315849304)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-12-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="289.7222204208374" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,300.7986092567444)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-13-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="133.4157943725586" y="289.7222204208374" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,300.7986092567444)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-13-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="21.56141471862793" y="289.7222204208374" x="192.07030487060547" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.07030487060547,300.7986092567444)" y="0" x="0" id="text-entity-Recipe-c5a555a1-f960-52db-b4ed-0bfa18575689-attr-13-key" class="er entityLabel"></text></g><g transform="translate(569.5068321228027,732.9861145019531 )" id="entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094"><rect height="289.7222204208374" width="229.0885362625122" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(114.5442681312561,11.944444179534912)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094" class="er entityLabel">Batch</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="147.95030212402344" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="22.483723640441895" y="23.888888359069824" x="206.6048126220703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,34.96527719497681)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-2-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="147.95030212402344" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-2-name" class="er entityLabel">batch_number</text><rect height="22.152777671813965" width="22.483723640441895" y="46.04166603088379" x="206.6048126220703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,57.11805486679077)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-2-key" class="er entityLabel">UK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-3-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="147.95030212402344" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-3-name" class="er entityLabel">recipe_id</text><rect height="22.152777671813965" width="22.483723640441895" y="68.19444370269775" x="206.6048126220703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,79.27083253860474)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-3-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-4-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="147.95030212402344" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-4-name" class="er entityLabel">user_id</text><rect height="22.152777671813965" width="22.483723640441895" y="90.34722137451172" x="206.6048126220703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,101.4236102104187)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-4-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-5-type" class="er entityLabel">enum</text><rect height="22.152777671813965" width="147.95030212402344" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-5-name" class="er entityLabel">status</text><rect height="22.152777671813965" width="22.483723640441895" y="112.49999904632568" x="206.6048126220703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,123.57638788223267)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-6-type" class="er entityLabel">date</text><rect height="22.152777671813965" width="147.95030212402344" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-6-name" class="er entityLabel">production_date</text><rect height="22.152777671813965" width="22.483723640441895" y="134.65277671813965" x="206.6048126220703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,145.72916555404663)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-7-type" class="er entityLabel">decimal</text><rect height="22.152777671813965" width="147.95030212402344" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-7-name" class="er entityLabel">quantity</text><rect height="22.152777671813965" width="22.483723640441895" y="156.8055543899536" x="206.6048126220703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,167.8819432258606)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-8-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="147.95030212402344" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-8-name" class="er entityLabel">actual_fermentation_duration</text><rect height="22.152777671813965" width="22.483723640441895" y="178.95833206176758" x="206.6048126220703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,190.03472089767456)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-9-type" class="er entityLabel">decimal</text><rect height="22.152777671813965" width="147.95030212402344" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-9-name" class="er entityLabel">actual_temperature</text><rect height="22.152777671813965" width="22.483723640441895" y="201.11110973358154" x="206.6048126220703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,212.18749856948853)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-9-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="223.2638874053955" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,234.3402762413025)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-10-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="147.95030212402344" y="223.2638874053955" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,234.3402762413025)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-10-name" class="er entityLabel">notes</text><rect height="22.152777671813965" width="22.483723640441895" y="223.2638874053955" x="206.6048126220703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,234.3402762413025)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-10-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="245.41666507720947" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,256.49305391311646)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-11-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="147.95030212402344" y="245.41666507720947" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,256.49305391311646)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-11-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="22.483723640441895" y="245.41666507720947" x="206.6048126220703" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,256.49305391311646)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-11-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="267.56944274902344" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,278.6458315849304)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-12-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="147.95030212402344" y="267.56944274902344" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,278.6458315849304)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-12-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="22.483723640441895" y="267.56944274902344" x="206.6048126220703" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(211.6048126220703,278.6458315849304)" y="0" x="0" id="text-entity-Batch-da7298ee-db40-5630-b5a9-7f4a015d8094-attr-12-key" class="er entityLabel"></text></g><g transform="translate(20,1167.013900756836 )" id="entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526"><rect height="267.56944274902344" width="153.59482765197754" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(76.79************,11.944444179534912)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526" class="er entityLabel">SensoryAssessment</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="73.37890243530273" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="21.56141471862793" y="23.888888359069824" x="132.0334129333496" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,34.96527719497681)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-2-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="73.37890243530273" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-2-name" class="er entityLabel">batch_id</text><rect height="22.152777671813965" width="21.56141471862793" y="46.04166603088379" x="132.0334129333496" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,57.11805486679077)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-2-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-3-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="73.37890243530273" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-3-name" class="er entityLabel">assessor_id</text><rect height="22.152777671813965" width="21.56141471862793" y="68.19444370269775" x="132.0334129333496" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,79.27083253860474)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-3-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-4-type" class="er entityLabel">smallint</text><rect height="22.152777671813965" width="73.37890243530273" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-4-name" class="er entityLabel">texture_score</text><rect height="22.152777671813965" width="21.56141471862793" y="90.34722137451172" x="132.0334129333496" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,101.4236102104187)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-5-type" class="er entityLabel">smallint</text><rect height="22.152777671813965" width="73.37890243530273" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-5-name" class="er entityLabel">acidity_score</text><rect height="22.152777671813965" width="21.56141471862793" y="112.49999904632568" x="132.0334129333496" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,123.57638788223267)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-6-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="73.37890243530273" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-6-name" class="er entityLabel">flavor_tags</text><rect height="22.152777671813965" width="21.56141471862793" y="134.65277671813965" x="132.0334129333496" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,145.72916555404663)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-7-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="73.37890243530273" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-7-name" class="er entityLabel">flavor_notes</text><rect height="22.152777671813965" width="21.56141471862793" y="156.8055543899536" x="132.0334129333496" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,167.8819432258606)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-8-type" class="er entityLabel">smallint</text><rect height="22.152777671813965" width="73.37890243530273" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-8-name" class="er entityLabel">overall_score</text><rect height="22.152777671813965" width="21.56141471862793" y="178.95833206176758" x="132.0334129333496" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,190.03472089767456)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-9-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="73.37890243530273" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-9-name" class="er entityLabel">notes</text><rect height="22.152777671813965" width="21.56141471862793" y="201.11110973358154" x="132.0334129333496" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,212.18749856948853)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-9-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="223.2638874053955" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,234.3402762413025)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-10-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="73.37890243530273" y="223.2638874053955" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,234.3402762413025)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-10-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="21.56141471862793" y="223.2638874053955" x="132.0334129333496" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,234.3402762413025)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-10-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="245.41666507720947" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,256.49305391311646)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-11-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="73.37890243530273" y="245.41666507720947" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,256.49305391311646)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-11-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="21.56141471862793" y="245.41666507720947" x="132.0334129333496" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(137.0334129333496,256.49305391311646)" y="0" x="0" id="text-entity-SensoryAssessment-7e62901f-5209-5a65-be86-d4db9921e526-attr-11-key" class="er entityLabel"></text></g><g transform="translate(965.1540832519531,1122.7083435058594 )" id="entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f"><rect height="356.1805534362793" width="165.97004508972168" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(82.98502254486084,11.944444179534912)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f" class="er entityLabel">MicroscopeImage</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="85.75411987304688" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="21.56141471862793" y="23.888888359069824" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,34.96527719497681)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-2-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="85.75411987304688" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-2-name" class="er entityLabel">batch_id</text><rect height="22.152777671813965" width="21.56141471862793" y="46.04166603088379" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,57.11805486679077)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-2-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-3-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="85.75411987304688" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-3-name" class="er entityLabel">filename</text><rect height="22.152777671813965" width="21.56141471862793" y="68.19444370269775" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,79.27083253860474)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-3-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-4-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="85.75411987304688" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-4-name" class="er entityLabel">original_name</text><rect height="22.152777671813965" width="21.56141471862793" y="90.34722137451172" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,101.4236102104187)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-5-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="85.75411987304688" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-5-name" class="er entityLabel">file_path</text><rect height="22.152777671813965" width="21.56141471862793" y="112.49999904632568" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,123.57638788223267)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-6-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="85.75411987304688" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-6-name" class="er entityLabel">file_size</text><rect height="22.152777671813965" width="21.56141471862793" y="134.65277671813965" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,145.72916555404663)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-7-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="85.75411987304688" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-7-name" class="er entityLabel">mime_type</text><rect height="22.152777671813965" width="21.56141471862793" y="156.8055543899536" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,167.8819432258606)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-8-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="85.75411987304688" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-8-name" class="er entityLabel">magnification</text><rect height="22.152777671813965" width="21.56141471862793" y="178.95833206176758" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,190.03472089767456)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-9-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="85.75411987304688" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-9-name" class="er entityLabel">staining_method</text><rect height="22.152777671813965" width="21.56141471862793" y="201.11110973358154" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,212.18749856948853)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-9-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="223.2638874053955" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,234.3402762413025)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-10-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="85.75411987304688" y="223.2638874053955" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,234.3402762413025)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-10-name" class="er entityLabel">image_width</text><rect height="22.152777671813965" width="21.56141471862793" y="223.2638874053955" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,234.3402762413025)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-10-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="245.41666507720947" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,256.49305391311646)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-11-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="85.75411987304688" y="245.41666507720947" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,256.49305391311646)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-11-name" class="er entityLabel">image_height</text><rect height="22.152777671813965" width="21.56141471862793" y="245.41666507720947" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,256.49305391311646)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-11-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="267.56944274902344" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,278.6458315849304)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-12-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="85.75411987304688" y="267.56944274902344" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,278.6458315849304)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-12-name" class="er entityLabel">metadata</text><rect height="22.152777671813965" width="21.56141471862793" y="267.56944274902344" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,278.6458315849304)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-12-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="289.7222204208374" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,300.7986092567444)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-13-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="85.75411987304688" y="289.7222204208374" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,300.7986092567444)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-13-name" class="er entityLabel">uploaded_by</text><rect height="22.152777671813965" width="21.56141471862793" y="289.7222204208374" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,300.7986092567444)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-13-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="311.87499809265137" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,322.95138692855835)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-14-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="85.75411987304688" y="311.87499809265137" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,322.95138692855835)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-14-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="21.56141471862793" y="311.87499809265137" x="144.40863037109375" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,322.95138692855835)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-14-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="334.02777576446533" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,345.1041646003723)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-15-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="85.75411987304688" y="334.02777576446533" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,345.1041646003723)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-15-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="21.56141471862793" y="334.02777576446533" x="144.40863037109375" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.40863037109375,345.1041646003723)" y="0" x="0" id="text-entity-MicroscopeImage-9dfab95e-f782-553d-b1a6-ae24e130688f-attr-15-key" class="er entityLabel"></text></g><g transform="translate(500.4291458129883,1155.937515258789 )" id="entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2"><rect height="289.7222204208374" width="152.69964790344238" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(76.34982395172119,11.944444179534912)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2" class="er entityLabel">QualityReport</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="72.48372268676758" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="21.56141471862793" y="23.888888359069824" x="131.13823318481445" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,34.96527719497681)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-2-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="72.48372268676758" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-2-name" class="er entityLabel">batch_id</text><rect height="22.152777671813965" width="21.56141471862793" y="46.04166603088379" x="131.13823318481445" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,57.11805486679077)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-2-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-3-type" class="er entityLabel">enum</text><rect height="22.152777671813965" width="72.48372268676758" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-3-name" class="er entityLabel">report_type</text><rect height="22.152777671813965" width="21.56141471862793" y="68.19444370269775" x="131.13823318481445" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,79.27083253860474)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-3-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-4-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="72.48372268676758" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-4-name" class="er entityLabel">title</text><rect height="22.152777671813965" width="21.56141471862793" y="90.34722137451172" x="131.13823318481445" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,101.4236102104187)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-5-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="72.48372268676758" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-5-name" class="er entityLabel">description</text><rect height="22.152777671813965" width="21.56141471862793" y="112.49999904632568" x="131.13823318481445" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,123.57638788223267)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-6-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="72.48372268676758" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-6-name" class="er entityLabel">date_range</text><rect height="22.152777671813965" width="21.56141471862793" y="134.65277671813965" x="131.13823318481445" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,145.72916555404663)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-7-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="72.48372268676758" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-7-name" class="er entityLabel">filters</text><rect height="22.152777671813965" width="21.56141471862793" y="156.8055543899536" x="131.13823318481445" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,167.8819432258606)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-8-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="72.48372268676758" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-8-name" class="er entityLabel">report_data</text><rect height="22.152777671813965" width="21.56141471862793" y="178.95833206176758" x="131.13823318481445" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,190.03472089767456)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-9-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="72.48372268676758" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-9-name" class="er entityLabel">generated_by</text><rect height="22.152777671813965" width="21.56141471862793" y="201.11110973358154" x="131.13823318481445" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,212.18749856948853)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-9-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="223.2638874053955" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,234.3402762413025)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-10-type" class="er entityLabel">boolean</text><rect height="22.152777671813965" width="72.48372268676758" y="223.2638874053955" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,234.3402762413025)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-10-name" class="er entityLabel">is_public</text><rect height="22.152777671813965" width="21.56141471862793" y="223.2638874053955" x="131.13823318481445" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,234.3402762413025)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-10-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="245.41666507720947" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,256.49305391311646)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-11-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="72.48372268676758" y="245.41666507720947" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,256.49305391311646)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-11-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="21.56141471862793" y="245.41666507720947" x="131.13823318481445" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,256.49305391311646)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-11-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="267.56944274902344" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,278.6458315849304)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-12-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="72.48372268676758" y="267.56944274902344" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,278.6458315849304)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-12-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="21.56141471862793" y="267.56944274902344" x="131.13823318481445" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(136.13823318481445,278.6458315849304)" y="0" x="0" id="text-entity-QualityReport-f29f8a7d-5105-5b55-bfc9-d4c800e771b2-attr-12-key" class="er entityLabel"></text></g><g transform="translate(157.39366149902344,365.4166717529297 )" id="entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968"><rect height="223.2638874053955" width="155.1573314666748" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(77.5786657333374,11.944444179534912)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968" class="er entityLabel">SystemLog</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="74.94140625" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="21.56141471862793" y="23.888888359069824" x="133.59591674804688" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,34.96527719497681)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-2-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="74.94140625" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-2-name" class="er entityLabel">user_id</text><rect height="22.152777671813965" width="21.56141471862793" y="46.04166603088379" x="133.59591674804688" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,57.11805486679077)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-2-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-3-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="74.94140625" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-3-name" class="er entityLabel">action</text><rect height="22.152777671813965" width="21.56141471862793" y="68.19444370269775" x="133.59591674804688" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,79.27083253860474)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-3-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-4-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="74.94140625" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-4-name" class="er entityLabel">resource_type</text><rect height="22.152777671813965" width="21.56141471862793" y="90.34722137451172" x="133.59591674804688" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,101.4236102104187)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-5-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="74.94140625" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-5-name" class="er entityLabel">resource_id</text><rect height="22.152777671813965" width="21.56141471862793" y="112.49999904632568" x="133.59591674804688" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,123.57638788223267)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-6-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="74.94140625" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-6-name" class="er entityLabel">details</text><rect height="22.152777671813965" width="21.56141471862793" y="134.65277671813965" x="133.59591674804688" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,145.72916555404663)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-7-type" class="er entityLabel">inet</text><rect height="22.152777671813965" width="74.94140625" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-7-name" class="er entityLabel">ip_address</text><rect height="22.152777671813965" width="21.56141471862793" y="156.8055543899536" x="133.59591674804688" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,167.8819432258606)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-8-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="74.94140625" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-8-name" class="er entityLabel">user_agent</text><rect height="22.152777671813965" width="21.56141471862793" y="178.95833206176758" x="133.59591674804688" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,190.03472089767456)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-9-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="74.94140625" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-9-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="21.56141471862793" y="201.11110973358154" x="133.59591674804688" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.59591674804688,212.18749856948853)" y="0" x="0" id="text-entity-SystemLog-1e539804-02e6-5c66-8bfd-669183cc4968-attr-9-key" class="er entityLabel"></text></g><g transform="translate(539.0304908752441,1578.888916015625 )" id="entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be"><rect height="444.79166412353516" width="213.69139671325684" y="0" x="0" class="er entityBox"></rect><text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(106.84569835662842,11.944444179534912)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be" class="er entityLabel">AIAnalysis</text><rect height="22.152777671813965" width="58.654510498046875" y="23.888888359069824" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,34.96527719497681)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-1-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="133.47547149658203" y="23.888888359069824" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,34.96527719497681)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-1-name" class="er entityLabel">id</text><rect height="22.152777671813965" width="21.56141471862793" y="23.888888359069824" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,34.96527719497681)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-1-key" class="er entityLabel">PK</text><rect height="22.152777671813965" width="58.654510498046875" y="46.04166603088379" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57.11805486679077)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-2-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="133.47547149658203" y="46.04166603088379" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,57.11805486679077)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-2-name" class="er entityLabel">batch_id</text><rect height="22.152777671813965" width="21.56141471862793" y="46.04166603088379" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,57.11805486679077)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-2-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="68.19444370269775" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79.27083253860474)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-3-type" class="er entityLabel">uuid</text><rect height="22.152777671813965" width="133.47547149658203" y="68.19444370269775" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,79.27083253860474)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-3-name" class="er entityLabel">microscope_image_id</text><rect height="22.152777671813965" width="21.56141471862793" y="68.19444370269775" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,79.27083253860474)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-3-key" class="er entityLabel">FK</text><rect height="22.152777671813965" width="58.654510498046875" y="90.34722137451172" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101.4236102104187)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-4-type" class="er entityLabel">enum</text><rect height="22.152777671813965" width="133.47547149658203" y="90.34722137451172" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,101.4236102104187)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-4-name" class="er entityLabel">analysis_status</text><rect height="22.152777671813965" width="21.56141471862793" y="90.34722137451172" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,101.4236102104187)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-4-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="112.49999904632568" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123.57638788223267)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-5-type" class="er entityLabel">decimal</text><rect height="22.152777671813965" width="133.47547149658203" y="112.49999904632568" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,123.57638788223267)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-5-name" class="er entityLabel">quality_score</text><rect height="22.152777671813965" width="21.56141471862793" y="112.49999904632568" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,123.57638788223267)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-5-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="134.65277671813965" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145.72916555404663)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-6-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="133.47547149658203" y="134.65277671813965" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,145.72916555404663)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-6-name" class="er entityLabel">bacterial_count</text><rect height="22.152777671813965" width="21.56141471862793" y="134.65277671813965" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,145.72916555404663)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-6-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="156.8055543899536" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167.8819432258606)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-7-type" class="er entityLabel">boolean</text><rect height="22.152777671813965" width="133.47547149658203" y="156.8055543899536" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,167.8819432258606)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-7-name" class="er entityLabel">contamination_detected</text><rect height="22.152777671813965" width="21.56141471862793" y="156.8055543899536" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,167.8819432258606)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-7-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="178.95833206176758" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,190.03472089767456)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-8-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="133.47547149658203" y="178.95833206176758" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,190.03472089767456)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-8-name" class="er entityLabel">contamination_type</text><rect height="22.152777671813965" width="21.56141471862793" y="178.95833206176758" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,190.03472089767456)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-8-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="201.11110973358154" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,212.18749856948853)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-9-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="133.47547149658203" y="201.11110973358154" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,212.18749856948853)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-9-name" class="er entityLabel">morphology_analysis</text><rect height="22.152777671813965" width="21.56141471862793" y="201.11110973358154" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,212.18749856948853)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-9-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="223.2638874053955" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,234.3402762413025)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-10-type" class="er entityLabel">decimal</text><rect height="22.152777671813965" width="133.47547149658203" y="223.2638874053955" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,234.3402762413025)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-10-name" class="er entityLabel">confidence_score</text><rect height="22.152777671813965" width="21.56141471862793" y="223.2638874053955" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,234.3402762413025)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-10-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="245.41666507720947" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,256.49305391311646)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-11-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="133.47547149658203" y="245.41666507720947" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,256.49305391311646)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-11-name" class="er entityLabel">summary_recommendation</text><rect height="22.152777671813965" width="21.56141471862793" y="245.41666507720947" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,256.49305391311646)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-11-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="267.56944274902344" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,278.6458315849304)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-12-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="133.47547149658203" y="267.56944274902344" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,278.6458315849304)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-12-name" class="er entityLabel">processing_time_ms</text><rect height="22.152777671813965" width="21.56141471862793" y="267.56944274902344" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,278.6458315849304)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-12-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="289.7222204208374" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,300.7986092567444)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-13-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="133.47547149658203" y="289.7222204208374" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,300.7986092567444)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-13-name" class="er entityLabel">model_version</text><rect height="22.152777671813965" width="21.56141471862793" y="289.7222204208374" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,300.7986092567444)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-13-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="311.87499809265137" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,322.95138692855835)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-14-type" class="er entityLabel">string</text><rect height="22.152777671813965" width="133.47547149658203" y="311.87499809265137" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,322.95138692855835)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-14-name" class="er entityLabel">api_provider</text><rect height="22.152777671813965" width="21.56141471862793" y="311.87499809265137" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,322.95138692855835)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-14-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="334.02777576446533" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,345.1041646003723)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-15-type" class="er entityLabel">jsonb</text><rect height="22.152777671813965" width="133.47547149658203" y="334.02777576446533" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,345.1041646003723)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-15-name" class="er entityLabel">raw_response</text><rect height="22.152777671813965" width="21.56141471862793" y="334.02777576446533" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,345.1041646003723)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-15-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="356.1805534362793" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,367.2569422721863)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-16-type" class="er entityLabel">text</text><rect height="22.152777671813965" width="133.47547149658203" y="356.1805534362793" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,367.2569422721863)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-16-name" class="er entityLabel">error_message</text><rect height="22.152777671813965" width="21.56141471862793" y="356.1805534362793" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,367.2569422721863)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-16-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="378.33333110809326" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,389.40971994400024)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-17-type" class="er entityLabel">integer</text><rect height="22.152777671813965" width="133.47547149658203" y="378.33333110809326" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,389.40971994400024)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-17-name" class="er entityLabel">retry_count</text><rect height="22.152777671813965" width="21.56141471862793" y="378.33333110809326" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,389.40971994400024)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-17-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="400.4861087799072" x="0" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,411.5624976158142)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-18-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="133.47547149658203" y="400.4861087799072" x="58.654510498046875" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,411.5624976158142)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-18-name" class="er entityLabel">created_at</text><rect height="22.152777671813965" width="21.56141471862793" y="400.4861087799072" x="192.1299819946289" class="er attributeBoxEven"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,411.5624976158142)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-18-key" class="er entityLabel"></text><rect height="22.152777671813965" width="58.654510498046875" y="422.6388864517212" x="0" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,433.7152752876282)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-19-type" class="er entityLabel">timestamp</text><rect height="22.152777671813965" width="133.47547149658203" y="422.6388864517212" x="58.654510498046875" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(63.654510498046875,433.7152752876282)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-19-name" class="er entityLabel">updated_at</text><rect height="22.152777671813965" width="21.56141471862793" y="422.6388864517212" x="192.1299819946289" class="er attributeBoxOdd"></rect><text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(197.1299819946289,433.7152752876282)" y="0" x="0" id="text-entity-AIAnalysis-7b79ae61-0234-53c9-8922-22cb90d977be-attr-19-key" class="er entityLabel"></text></g><rect height="13.888888359069824" width="39.615882873535156" y="217.2806077003479" x="666.2557182312012" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="224.2250518798828" x="686.0636596679688" id="rel1" class="er relationshipLabel">creates</text><rect height="13.888888359069824" width="46.554901123046875" y="468.62153482437134" x="557.3653717041016" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="475.56597900390625" x="580.642822265625" id="rel2" class="er relationshipLabel">manages</text><rect height="13.888888359069824" width="51.291229248046875" y="551.3215470314026" x="21.151390075683594" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="558.2659912109375" x="46.79700469970703" id="rel3" class="er relationshipLabel">evaluates</text><rect height="13.888888359069824" width="41.06987762451172" y="485.7341446876526" x="977.6040382385254" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="492.6785888671875" x="998.1389770507812" id="rel4" class="er relationshipLabel">uploads</text><rect height="13.888888359069824" width="52.799476623535156" y="704.9471573829651" x="396.97126388549805" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="711.8916015625" x="423.3710021972656" id="rel5" class="er relationshipLabel">generates</text><rect height="13.888888359069824" width="48.25303649902344" y="240.13928079605103" x="259.9343032836914" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="247.08372497558594" x="284.0608215332031" id="rel6" class="er relationshipLabel">performs</text><rect height="13.888888359069824" width="22.81900978088379" y="678.1822037696838" x="771.0350751876831" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="685.1266479492188" x="782.444580078125" id="rel7" class="er relationshipLabel">uses</text><rect height="13.888888359069824" width="17.71918296813965" y="1004.9895157814026" x="314.4659090042114" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1011.9339599609375" x="323.32550048828125" id="rel8" class="er relationshipLabel">has</text><rect height="13.888888359069824" width="44.840492248535156" y="1003.2053360939026" x="939.3268852233887" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1010.1497802734375" x="961.7471313476562" id="rel9" class="er relationshipLabel">contains</text><rect height="13.888888359069824" width="45.724822998046875" y="1300.2619767189026" x="262.3347930908203" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1307.2064208984375" x="285.19720458984375" id="rel10" class="er relationshipLabel">analyzes</text><rect height="13.888888359069824" width="48.3778190612793" y="1084.673231601715" x="648.9122257232666" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1091.61767578125" x="673.1011352539062" id="rel11" class="er relationshipLabel">produces</text><rect height="13.888888359069824" width="52.799476623535156" y="1610.00013589859" x="889.4238090515137" class="er relationshipLabelBox"></rect><text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1616.944580078125" x="915.8235473632812" id="rel12" class="er relationshipLabel">generates</text></svg>