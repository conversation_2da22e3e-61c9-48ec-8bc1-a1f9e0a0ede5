import React, { useState } from 'react'
import { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Badge, Space } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import {
  DashboardOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  RobotOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons'

import { logout } from '../../store/slices/authSlice'
import { useTypedSelector } from '../../hooks/useTypedSelector'
import './Layout.css'

const { Header, Sider, Content } = AntLayout

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { user } = useTypedSelector((state) => state.auth)

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/recipes',
      icon: <ExperimentOutlined />,
      label: '配方管理',
    },
    {
      key: '/batches',
      icon: <DatabaseOutlined />,
      label: '批次管理',
    },
    {
      key: '/analysis',
      icon: <RobotOutlined />,
      label: 'AI分析',
    },
    {
      key: '/reports',
      icon: <FileTextOutlined />,
      label: '报告中心',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ]

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        dispatch(logout())
        navigate('/login')
      },
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <AntLayout className="layout-container">
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="layout-sider"
        width={240}
        collapsedWidth={80}
      >
        {/* Logo区域 */}
        <div className="layout-logo">
          <div className="logo-icon">🥛</div>
          {!collapsed && (
            <div className="logo-text">
              <div className="logo-title">Yogurt AI QC</div>
              <div className="logo-subtitle">酸奶质控系统</div>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="layout-menu"
        />
      </Sider>

      {/* 主内容区域 */}
      <AntLayout className="layout-main">
        {/* 顶部导航栏 */}
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="collapse-trigger"
            />
          </div>

          <div className="header-right">
            <Space size="middle">
              {/* 通知铃铛 */}
              <Badge count={3} size="small">
                <Button 
                  type="text" 
                  icon={<BellOutlined />} 
                  className="header-action"
                />
              </Badge>

              {/* 用户信息 */}
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <div className="user-info">
                  <Avatar 
                    size="small" 
                    icon={<UserOutlined />}
                    style={{ backgroundColor: '#F05654' }}
                  />
                  <span className="user-name">{user?.name || '用户'}</span>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* 页面内容 */}
        <Content className="layout-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  )
}

export default Layout
