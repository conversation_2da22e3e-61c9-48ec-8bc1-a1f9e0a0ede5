/* =============================================================================
   Login 页面样式
   ============================================================================= */

.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 背景 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #F05654 0%, #FF6B69 50%, #A8ABB0 100%);
  z-index: 1;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* 主内容 */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 480px;
  padding: 24px;
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
}

.login-card .ant-card-body {
  padding: 0;
}

/* 头部 */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.login-logo .logo-icon {
  font-size: 48px;
  margin-right: 16px;
}

.login-logo .logo-text {
  text-align: left;
}

.login-logo .logo-title {
  color: #F05654 !important;
  margin: 0 !important;
  font-weight: 700;
  font-size: 28px;
}

.login-logo .logo-subtitle {
  color: #A8ABB0;
  font-size: 14px;
  font-weight: 500;
}

.login-description {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

/* 表单标签页 */
.form-tabs {
  display: flex;
  margin-bottom: 32px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  border: none !important;
  border-radius: 6px !important;
  font-weight: 500;
  transition: all 0.3s;
}

.tab-button.ant-btn-primary {
  background: #F05654 !important;
  box-shadow: 0 2px 8px rgba(240, 86, 84, 0.3);
}

.tab-button.ant-btn-text {
  color: #666;
}

.tab-button.ant-btn-text:hover {
  color: #F05654 !important;
  background: rgba(240, 86, 84, 0.1) !important;
}

/* 表单 */
.login-form {
  margin-top: 24px;
}

.login-form-container {
  margin-bottom: 16px;
}

.login-form .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.login-form .ant-input-affix-wrapper {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused {
  border-color: #F05654;
  box-shadow: 0 0 0 2px rgba(240, 86, 84, 0.2);
}

.login-form .ant-input-affix-wrapper .ant-input {
  font-size: 16px;
}

.login-form .ant-input-prefix {
  color: #A8ABB0;
  margin-right: 12px;
}

/* 提交按钮 */
.submit-button {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  background: #F05654 !important;
  border-color: #F05654 !important;
  margin-top: 16px;
  transition: all 0.3s;
}

.submit-button:hover {
  background: #FF6B69 !important;
  border-color: #FF6B69 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 86, 84, 0.3);
}

.submit-button:active {
  transform: translateY(0);
}

/* 登录页脚 */
.login-footer {
  text-align: center;
  margin-top: 32px;
}

.forgot-password {
  color: #F05654 !important;
  font-weight: 500;
}

.forgot-password:hover {
  color: #FF6B69 !important;
}



/* 页面页脚 */
.login-page-footer {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 2;
}

.login-page-footer .ant-typography {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    max-width: 100%;
    padding: 16px;
  }
  
  .login-card {
    padding: 24px;
    margin: 16px 0;
  }
  
  .login-logo {
    flex-direction: column;
    text-align: center;
  }
  
  .login-logo .logo-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .login-logo .logo-text {
    text-align: center;
  }
  
  .login-page-footer {
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    margin-top: 24px;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 20px;
  }
  
  .login-logo .logo-title {
    font-size: 24px;
  }
  
  .login-description {
    font-size: 14px;
  }
  
  .form-tabs {
    margin-bottom: 24px;
  }
  
  .submit-button {
    height: 44px;
    font-size: 15px;
  }
}

/* 动画效果 */
.login-card {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框聚焦动画 */
.login-form .ant-input-affix-wrapper {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused {
  transform: translateY(-1px);
}

/* 加载状态 */
.submit-button.ant-btn-loading {
  background: #A8ABB0 !important;
  border-color: #A8ABB0 !important;
}

/* 错误状态 */
.login-form .ant-form-item-has-error .ant-input-affix-wrapper {
  border-color: #ff4d4f;
}

.login-form .ant-form-item-has-error .ant-input-affix-wrapper:focus,
.login-form .ant-form-item-has-error .ant-input-affix-wrapper-focused {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}
