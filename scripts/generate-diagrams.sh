#!/bin/bash

# =============================================================================
# 生成数据库架构图片脚本
# =============================================================================

echo "🎨 生成数据库架构图片..."

# 检查是否安装了 mermaid-cli
if ! command -v mmdc &> /dev/null; then
    echo "📦 安装 Mermaid CLI..."
    npm install -g @mermaid-js/mermaid-cli
fi

# 创建输出目录
mkdir -p docs/images

# 生成实体关系图
echo "📊 生成实体关系图..."
mmdc -i docs/database-schema.md -o docs/images/database-er-diagram.png -t dark -b white

# 生成数据流程图
echo "🔄 生成数据流程图..."
cat > temp-flow.mmd << 'EOF'
flowchart TD
    A[用户登录] --> B[创建配方]
    B --> C[开始新批次]
    C --> D[生产酸奶]
    D --> E[感官评估]
    D --> F[显微镜拍照]
    F --> G[上传图像]
    G --> H[AI分析]
    H --> I[生成分析报告]
    E --> J[记录评估结果]
    I --> K[质量报告]
    J --> K
    K --> L[数据看板]
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style K fill:#f3e5f5
    style L fill:#e8f5e8
EOF

mmdc -i temp-flow.mmd -o docs/images/data-flow-diagram.png -t dark -b white

# 生成索引设计图
echo "🗂️ 生成索引设计图..."
cat > temp-index.mmd << 'EOF'
graph TD
    subgraph "用户表索引"
        U1[email - 唯一索引]
        U2[role - 普通索引]
        U3[is_active - 普通索引]
        U4[created_at - 普通索引]
    end
    
    subgraph "批次表索引"
        B1[batch_number - 唯一索引]
        B2[recipe_id - 外键索引]
        B3[user_id - 外键索引]
        B4[status - 普通索引]
        B5[production_date - 普通索引]
        B6[user_id + status - 组合索引]
        B7[recipe_id + status - 组合索引]
    end
    
    subgraph "AI分析表索引"
        A1[batch_id - 外键索引]
        A2[analysis_status - 普通索引]
        A3[quality_score - 普通索引]
        A4[contamination_detected - 普通索引]
        A5[batch_id + analysis_status - 组合索引]
    end
    
    style U1 fill:#ffcdd2
    style B1 fill:#ffcdd2
    style B6 fill:#c8e6c9
    style B7 fill:#c8e6c9
    style A5 fill:#c8e6c9
EOF

mmdc -i temp-index.mmd -o docs/images/index-design-diagram.png -t dark -b white

# 清理临时文件
rm -f temp-*.mmd

echo "✅ 图片生成完成！"
echo "📁 图片保存在 docs/images/ 目录下："
echo "   - database-er-diagram.png (实体关系图)"
echo "   - data-flow-diagram.png (数据流程图)"
echo "   - index-design-diagram.png (索引设计图)"
