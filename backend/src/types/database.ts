// =============================================================================
// Yoghurt AI QC - 数据库类型定义
// =============================================================================

// 基础实体接口
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// 用户相关类型
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER', 
  VIEWER = 'VIEWER'
}

export interface User extends BaseEntity {
  email: string
  passwordHash: string
  name: string
  role: UserRole
  isActive: boolean
}

// 配方相关类型
export interface Ingredient {
  name: string
  amount: number
  unit: string
}

export interface ProcessStep {
  step: number
  description: string
  duration?: number // 分钟
  temperature?: number // 摄氏度
}

export interface Recipe extends BaseEntity {
  userId: string
  name: string
  description?: string
  ingredients: Ingredient[]
  process: ProcessStep[]
  fermentationTemperature?: number
  fermentationDuration?: number // 小时
  filtrationDuration?: number // 小时
  version: number
  isActive: boolean
  
  // 关联数据
  user?: User
  batches?: Batch[]
}

// 批次相关类型
export enum BatchStatus {
  PLANNING = 'PLANNING',
  IN_PROGRESS = 'IN_PROGRESS',
  FERMENTING = 'FERMENTING',
  FILTERING = 'FILTERING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface Batch extends BaseEntity {
  batchNumber: string
  recipeId: string
  userId: string
  status: BatchStatus
  productionDate: Date
  quantity?: number // L 或 kg
  actualFermentationDuration?: number // 小时
  actualTemperature?: number
  notes?: string
  
  // 关联数据
  recipe?: Recipe
  user?: User
  sensoryAssessments?: SensoryAssessment[]
  microscopeImages?: MicroscopeImage[]
  aiAnalyses?: AIAnalysis[]
  qualityReports?: QualityReport[]
}

// 感官评估类型
export interface SensoryAssessment extends BaseEntity {
  batchId: string
  assessorId: string
  textureScore: number // 1-5
  acidityScore: number // 1-5
  flavorTags: string[]
  flavorNotes?: string
  overallScore: number // 1-5
  notes?: string
  
  // 关联数据
  batch?: Batch
  assessor?: User
}

// 显微镜图像类型
export interface MicroscopeImage extends BaseEntity {
  batchId: string
  filename: string
  originalName: string
  filePath: string
  fileSize: number // bytes
  mimeType: string
  magnification: number
  stainingMethod: string
  imageWidth?: number
  imageHeight?: number
  metadata?: Record<string, any>
  uploadedBy: string
  
  // 关联数据
  batch?: Batch
  uploader?: User
  aiAnalyses?: AIAnalysis[]
}

// AI分析类型
export enum AnalysisStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export interface MorphologyAnalysis {
  bacterialMorphology: string
  balanceRatio: string
  activityLevel: string
}

export interface AIAnalysis extends BaseEntity {
  batchId: string
  microscopeImageId?: string
  analysisStatus: AnalysisStatus
  qualityScore?: number // 0-100
  bacterialCount?: number
  contaminationDetected: boolean
  contaminationType?: string
  morphologyAnalysis?: MorphologyAnalysis
  confidenceScore?: number // 0-100
  summaryRecommendation?: string
  processingTimeMs?: number
  modelVersion?: string
  apiProvider?: string
  rawResponse?: Record<string, any>
  errorMessage?: string
  retryCount: number
  
  // 关联数据
  batch?: Batch
  microscopeImage?: MicroscopeImage
}

// 质量报告类型
export enum ReportType {
  QUALITY_SUMMARY = 'QUALITY_SUMMARY',
  BATCH_ANALYSIS = 'BATCH_ANALYSIS',
  TREND_ANALYSIS = 'TREND_ANALYSIS',
  CONTAMINATION_REPORT = 'CONTAMINATION_REPORT',
  CUSTOM = 'CUSTOM'
}

export interface QualityReport extends BaseEntity {
  batchId?: string
  reportType: ReportType
  title: string
  description?: string
  dateRange?: {
    start: Date
    end: Date
  }
  filters?: Record<string, any>
  reportData: Record<string, any>
  generatedBy: string
  isPublic: boolean
  
  // 关联数据
  batch?: Batch
  generator?: User
}

// 系统日志类型
export interface SystemLog {
  id: string
  userId?: string
  action: string
  resourceType?: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt: Date
  
  // 关联数据
  user?: User
}

// 查询参数类型
export interface PaginationParams {
  page?: number
  pageSize?: number
}

export interface SortParams {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface DateRangeParams {
  dateRange?: [Date, Date]
}

// 用户查询参数
export interface UserQueryParams extends PaginationParams, SortParams {
  search?: string
  role?: UserRole
  isActive?: boolean
}

// 配方查询参数
export interface RecipeQueryParams extends PaginationParams, SortParams {
  search?: string
  userId?: string
  isActive?: boolean
}

// 批次查询参数
export interface BatchQueryParams extends PaginationParams, SortParams, DateRangeParams {
  search?: string
  status?: BatchStatus
  recipeId?: string
  userId?: string
}

// 感官评估查询参数
export interface SensoryAssessmentQueryParams extends PaginationParams, SortParams, DateRangeParams {
  batchId?: string
  assessorId?: string
  minOverallScore?: number
  maxOverallScore?: number
}

// AI分析查询参数
export interface AIAnalysisQueryParams extends PaginationParams, SortParams, DateRangeParams {
  batchId?: string
  analysisStatus?: AnalysisStatus
  contaminationDetected?: boolean
  minQualityScore?: number
  maxQualityScore?: number
}

// 质量报告查询参数
export interface QualityReportQueryParams extends PaginationParams, SortParams, DateRangeParams {
  reportType?: ReportType
  generatedBy?: string
  isPublic?: boolean
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    current: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 创建请求类型
export interface CreateUserRequest {
  email: string
  password: string
  name: string
  role?: UserRole
  isActive?: boolean
}

export interface CreateRecipeRequest {
  name: string
  description?: string
  ingredients: Ingredient[]
  process: ProcessStep[]
  fermentationTemperature?: number
  fermentationDuration?: number
  filtrationDuration?: number
}

export interface CreateBatchRequest {
  recipeId: string
  quantity?: number
  notes?: string
}

export interface CreateSensoryAssessmentRequest {
  textureScore: number
  acidityScore: number
  flavorTags: string[]
  flavorNotes?: string
  overallScore: number
  notes?: string
}

// 更新请求类型
export interface UpdateUserRequest {
  email?: string
  name?: string
  role?: UserRole
  isActive?: boolean
}

export interface UpdateRecipeRequest {
  name?: string
  description?: string
  ingredients?: Ingredient[]
  process?: ProcessStep[]
  fermentationTemperature?: number
  fermentationDuration?: number
  filtrationDuration?: number
  isActive?: boolean
}

export interface UpdateBatchRequest {
  status?: BatchStatus
  quantity?: number
  actualFermentationDuration?: number
  actualTemperature?: number
  notes?: string
}
