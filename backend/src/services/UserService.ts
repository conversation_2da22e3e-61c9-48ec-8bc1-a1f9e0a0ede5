// =============================================================================
// Yoghurt AI QC - 用户服务
// =============================================================================

import bcrypt from 'bcryptjs'
import prisma from '../lib/prisma'
import { BaseService } from './BaseService'
import { 
  User, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserQueryParams,
  UserRole 
} from '../types/database'
import { logger } from '../utils/logger'

export class UserService extends BaseService<User, CreateUserRequest, UpdateUserRequest, UserQueryParams> {
  protected modelName = 'User'
  protected model = prisma.user

  // 获取默认包含的关联数据
  protected getDefaultInclude() {
    return {
      _count: {
        select: {
          recipes: true,
          batches: true,
          sensoryAssessments: true,
          qualityReports: true,
        },
      },
    }
  }

  // 构建查询条件
  protected buildWhereClause(filters: Partial<UserQueryParams>) {
    const { search, role, isActive, ...otherFilters } = filters
    
    const where: any = {}

    // 搜索条件
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ]
    }

    // 角色过滤
    if (role) {
      where.role = role
    }

    // 活跃状态过滤
    if (typeof isActive === 'boolean') {
      where.isActive = isActive
    }

    return where
  }

  // 构建排序条件
  protected buildOrderByClause(sortBy: string, sortOrder: 'asc' | 'desc') {
    const validSortFields = ['name', 'email', 'role', 'createdAt', 'updatedAt']
    
    if (!validSortFields.includes(sortBy)) {
      sortBy = 'createdAt'
    }

    return { [sortBy]: sortOrder }
  }

  // 创建用户（重写以处理密码加密）
  async create(data: CreateUserRequest): Promise<User> {
    try {
      // 检查邮箱是否已存在
      const existingUser = await this.findByEmail(data.email)
      if (existingUser) {
        throw new Error('Email already exists')
      }

      // 加密密码
      const saltRounds = 12
      const passwordHash = await bcrypt.hash(data.password, saltRounds)

      const createData = {
        email: data.email,
        passwordHash,
        name: data.name,
        role: data.role || UserRole.USER,
        isActive: data.isActive !== undefined ? data.isActive : true,
      }

      const result = await this.model.create({
        data: createData,
        include: this.getDefaultInclude(),
      })

      logger.info('User created', { id: result.id, email: result.email })
      return result
    } catch (error) {
      logger.error('Failed to create user:', error)
      throw error
    }
  }

  // 根据邮箱查找用户
  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await this.model.findUnique({
        where: { email },
        include: this.getDefaultInclude(),
      })

      return result
    } catch (error) {
      logger.error('Failed to find user by email:', error)
      throw error
    }
  }

  // 验证用户密码
  async validatePassword(email: string, password: string): Promise<User | null> {
    try {
      const user = await this.model.findUnique({
        where: { email },
        include: this.getDefaultInclude(),
      })

      if (!user) {
        return null
      }

      const isPasswordValid = await bcrypt.compare(password, user.passwordHash)
      if (!isPasswordValid) {
        return null
      }

      return user
    } catch (error) {
      logger.error('Failed to validate user password:', error)
      throw error
    }
  }

  // 更新用户密码
  async updatePassword(id: string, newPassword: string): Promise<User> {
    try {
      const saltRounds = 12
      const passwordHash = await bcrypt.hash(newPassword, saltRounds)

      const result = await this.model.update({
        where: { id },
        data: { passwordHash },
        include: this.getDefaultInclude(),
      })

      logger.info('User password updated', { id })
      return result
    } catch (error) {
      logger.error('Failed to update user password:', error)
      throw error
    }
  }

  // 激活/停用用户
  async toggleActive(id: string): Promise<User> {
    try {
      const user = await this.findById(id)
      if (!user) {
        throw new Error('User not found')
      }

      const result = await this.model.update({
        where: { id },
        data: { isActive: !user.isActive },
        include: this.getDefaultInclude(),
      })

      logger.info('User active status toggled', { id, isActive: result.isActive })
      return result
    } catch (error) {
      logger.error('Failed to toggle user active status:', error)
      throw error
    }
  }

  // 获取用户统计信息
  async getUserStats(id: string) {
    try {
      const stats = await prisma.user.findUnique({
        where: { id },
        select: {
          _count: {
            select: {
              recipes: true,
              batches: true,
              sensoryAssessments: true,
              qualityReports: true,
              systemLogs: true,
            },
          },
        },
      })

      if (!stats) {
        throw new Error('User not found')
      }

      // 获取最近的活动
      const recentBatches = await prisma.batch.findMany({
        where: { userId: id },
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          recipe: {
            select: { name: true },
          },
        },
      })

      const recentAssessments = await prisma.sensoryAssessment.findMany({
        where: { assessorId: id },
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          batch: {
            select: { batchNumber: true },
          },
        },
      })

      return {
        counts: stats._count,
        recentBatches,
        recentAssessments,
      }
    } catch (error) {
      logger.error('Failed to get user stats:', error)
      throw error
    }
  }

  // 获取用户角色统计
  async getRoleStats() {
    try {
      const roleStats = await prisma.user.groupBy({
        by: ['role'],
        _count: {
          role: true,
        },
      })

      const activeStats = await prisma.user.groupBy({
        by: ['isActive'],
        _count: {
          isActive: true,
        },
      })

      return {
        byRole: roleStats,
        byActiveStatus: activeStats,
      }
    } catch (error) {
      logger.error('Failed to get role stats:', error)
      throw error
    }
  }

  // 检查邮箱是否可用
  async isEmailAvailable(email: string, excludeUserId?: string): Promise<boolean> {
    try {
      const where: any = { email }
      
      if (excludeUserId) {
        where.id = { not: excludeUserId }
      }

      const existingUser = await this.model.findFirst({ where })
      return !existingUser
    } catch (error) {
      logger.error('Failed to check email availability:', error)
      throw error
    }
  }

  // 批量导入用户
  async importUsers(users: CreateUserRequest[]): Promise<{ success: number; failed: number; errors: string[] }> {
    let success = 0
    let failed = 0
    const errors: string[] = []

    for (const userData of users) {
      try {
        await this.create(userData)
        success++
      } catch (error) {
        failed++
        errors.push(`Failed to create user ${userData.email}: ${error.message}`)
      }
    }

    logger.info('User import completed', { success, failed, total: users.length })
    
    return { success, failed, errors }
  }

  // 获取最近注册的用户
  async getRecentUsers(limit = 10): Promise<User[]> {
    try {
      const result = await this.model.findMany({
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: this.getDefaultInclude(),
      })

      return result
    } catch (error) {
      logger.error('Failed to get recent users:', error)
      throw error
    }
  }
}
