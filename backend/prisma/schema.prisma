// =============================================================================
// Yoghurt AI QC - Prisma Schema
// =============================================================================

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =============================================================================
// 用户管理
// =============================================================================

model User {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email     String   @unique @db.VarChar(255)
  passwordHash String @map("password_hash") @db.VarChar(255)
  name      String   @db.VarChar(100)
  role      UserRole @default(USER)
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  recipes           Recipe[]
  batches           Batch[]
  sensoryAssessments SensoryAssessment[]
  qualityReports    QualityReport[]
  systemLogs        SystemLog[]
  uploadedImages    MicroscopeImage[]

  // 索引
  @@index([email])
  @@index([role])
  @@index([isActive])
  @@index([createdAt])
  @@map("users")
}

enum UserRole {
  ADMIN
  USER
  VIEWER

  @@map("user_role")
}

// =============================================================================
// 配方管理
// =============================================================================

model Recipe {
  id          String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId      String  @map("user_id") @db.Uuid
  name        String  @db.VarChar(100)
  description String? @db.Text
  ingredients Json    @db.JsonB
  process     Json    @db.JsonB
  fermentationTemperature Decimal? @map("fermentation_temperature") @db.Decimal(4, 2)
  fermentationDuration    Int?     @map("fermentation_duration") // 小时
  filtrationDuration      Int?     @map("filtration_duration") // 小时
  version     Int     @default(1)
  isActive    Boolean @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  batches Batch[]

  // 索引
  @@index([userId])
  @@index([name])
  @@index([isActive])
  @@index([createdAt])
  @@index([userId, isActive])
  @@map("recipes")
}

// =============================================================================
// 批次管理
// =============================================================================

model Batch {
  id           String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchNumber  String      @unique @map("batch_number") @db.VarChar(50)
  recipeId     String      @map("recipe_id") @db.Uuid
  userId       String      @map("user_id") @db.Uuid
  status       BatchStatus @default(PLANNING)
  productionDate DateTime  @map("production_date") @db.Date
  quantity     Decimal?    @db.Decimal(8, 2) // L 或 kg
  actualFermentationDuration Int? @map("actual_fermentation_duration") // 小时
  actualTemperature          Decimal? @map("actual_temperature") @db.Decimal(4, 2)
  notes        String?     @db.Text
  createdAt    DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt    DateTime    @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  recipe             Recipe              @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  sensoryAssessments SensoryAssessment[]
  microscopeImages   MicroscopeImage[]
  aiAnalyses         AIAnalysis[]
  qualityReports     QualityReport[]

  // 索引
  @@index([batchNumber])
  @@index([recipeId])
  @@index([userId])
  @@index([status])
  @@index([productionDate])
  @@index([createdAt])
  @@index([userId, status])
  @@index([recipeId, status])
  @@index([productionDate, status])
  @@map("batches")
}

enum BatchStatus {
  PLANNING
  IN_PROGRESS
  FERMENTING
  FILTERING
  COMPLETED
  FAILED

  @@map("batch_status")
}

// =============================================================================
// 感官评估
// =============================================================================

model SensoryAssessment {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId      String   @map("batch_id") @db.Uuid
  assessorId   String   @map("assessor_id") @db.Uuid
  textureScore Int      @map("texture_score") @db.SmallInt // 1-5
  acidityScore Int      @map("acidity_score") @db.SmallInt // 1-5
  flavorTags   Json     @map("flavor_tags") @db.JsonB
  flavorNotes  String?  @map("flavor_notes") @db.Text
  overallScore Int      @map("overall_score") @db.SmallInt // 1-5
  notes        String?  @db.Text
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt    DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  batch    Batch @relation(fields: [batchId], references: [id], onDelete: Cascade)
  assessor User  @relation(fields: [assessorId], references: [id], onDelete: Cascade)

  // 索引
  @@index([batchId])
  @@index([assessorId])
  @@index([overallScore])
  @@index([createdAt])
  @@index([batchId, createdAt])
  @@map("sensory_assessments")
}

// =============================================================================
// 显微镜图像管理
// =============================================================================

model MicroscopeImage {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId        String   @map("batch_id") @db.Uuid
  filename       String   @db.VarChar(255)
  originalName   String   @map("original_name") @db.VarChar(255)
  filePath       String   @map("file_path") @db.VarChar(500)
  fileSize       Int      @map("file_size") // bytes
  mimeType       String   @map("mime_type") @db.VarChar(100)
  magnification  Int      // 放大倍数
  stainingMethod String   @map("staining_method") @db.VarChar(100)
  imageWidth     Int?     @map("image_width")
  imageHeight    Int?     @map("image_height")
  metadata       Json?    @db.JsonB // 额外的图像元数据
  uploadedBy     String   @map("uploaded_by") @db.Uuid
  createdAt      DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  batch       Batch        @relation(fields: [batchId], references: [id], onDelete: Cascade)
  uploader    User         @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)
  aiAnalyses  AIAnalysis[]

  // 索引
  @@index([batchId])
  @@index([uploadedBy])
  @@index([magnification])
  @@index([stainingMethod])
  @@index([createdAt])
  @@index([batchId, createdAt])
  @@map("microscope_images")
}

// =============================================================================
// AI分析结果
// =============================================================================

model AIAnalysis {
  id                    String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId               String        @map("batch_id") @db.Uuid
  microscopeImageId     String?       @map("microscope_image_id") @db.Uuid
  analysisStatus        AnalysisStatus @default(PENDING) @map("analysis_status")
  qualityScore          Decimal?      @map("quality_score") @db.Decimal(5, 2) // 0-100
  bacterialCount        Int?          @map("bacterial_count")
  contaminationDetected Boolean       @default(false) @map("contamination_detected")
  contaminationType     String?       @map("contamination_type") @db.VarChar(100)
  morphologyAnalysis    Json?         @map("morphology_analysis") @db.JsonB
  confidenceScore       Decimal?      @map("confidence_score") @db.Decimal(5, 2) // 0-100
  summaryRecommendation String?       @map("summary_recommendation") @db.Text
  processingTimeMs      Int?          @map("processing_time_ms")
  modelVersion          String?       @map("model_version") @db.VarChar(50)
  apiProvider           String?       @map("api_provider") @db.VarChar(50)
  rawResponse           Json?         @map("raw_response") @db.JsonB // 原始AI响应
  errorMessage          String?       @map("error_message") @db.Text
  retryCount            Int           @default(0) @map("retry_count")
  createdAt             DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt             DateTime      @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  batch           Batch            @relation(fields: [batchId], references: [id], onDelete: Cascade)
  microscopeImage MicroscopeImage? @relation(fields: [microscopeImageId], references: [id], onDelete: SetNull)

  // 索引
  @@index([batchId])
  @@index([microscopeImageId])
  @@index([analysisStatus])
  @@index([qualityScore])
  @@index([contaminationDetected])
  @@index([createdAt])
  @@index([batchId, analysisStatus])
  @@index([analysisStatus, createdAt])
  @@map("ai_analyses")
}

enum AnalysisStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED

  @@map("analysis_status")
}

// =============================================================================
// 质量报告
// =============================================================================

model QualityReport {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  batchId     String?    @map("batch_id") @db.Uuid
  reportType  ReportType @map("report_type")
  title       String     @db.VarChar(200)
  description String?    @db.Text
  dateRange   Json?      @map("date_range") @db.JsonB // {start: date, end: date}
  filters     Json?      @db.JsonB // 报告筛选条件
  reportData  Json       @map("report_data") @db.JsonB // 报告内容数据
  generatedBy String     @map("generated_by") @db.Uuid
  isPublic    Boolean    @default(false) @map("is_public")
  createdAt   DateTime   @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime   @updatedAt @map("updated_at") @db.Timestamp(6)

  // 关联关系
  batch     Batch? @relation(fields: [batchId], references: [id], onDelete: SetNull)
  generator User   @relation(fields: [generatedBy], references: [id], onDelete: Cascade)

  // 索引
  @@index([batchId])
  @@index([reportType])
  @@index([generatedBy])
  @@index([isPublic])
  @@index([createdAt])
  @@index([reportType, createdAt])
  @@map("quality_reports")
}

enum ReportType {
  QUALITY_SUMMARY
  BATCH_ANALYSIS
  TREND_ANALYSIS
  CONTAMINATION_REPORT
  CUSTOM

  @@map("report_type")
}

// =============================================================================
// 系统日志
// =============================================================================

model SystemLog {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String?   @map("user_id") @db.Uuid
  action       String    @db.VarChar(100)
  resourceType String?   @map("resource_type") @db.VarChar(50)
  resourceId   String?   @map("resource_id") @db.Uuid
  details      Json?     @db.JsonB
  ipAddress    String?   @map("ip_address") @db.Inet
  userAgent    String?   @map("user_agent") @db.Text
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamp(6)

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // 索引
  @@index([userId])
  @@index([action])
  @@index([resourceType])
  @@index([resourceId])
  @@index([createdAt])
  @@index([userId, action])
  @@index([action, createdAt])
  @@map("system_logs")
}
