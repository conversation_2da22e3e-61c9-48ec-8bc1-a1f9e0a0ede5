#!/bin/bash

# =============================================================================
# Prisma Studio 启动脚本
# =============================================================================

echo "🚀 启动 Prisma Studio..."

# 设置环境变量
export DATABASE_URL="postgresql://postgres:postgres@localhost:6432/postgres"

# 进入正确的目录
cd "$(dirname "$0")"

# 检查schema文件是否存在
if [ ! -f "prisma/schema.prisma" ]; then
    echo "❌ 错误: 找不到 prisma/schema.prisma 文件"
    exit 1
fi

# 启动 Prisma Studio
echo "📊 Prisma Studio 将在 http://localhost:5555 启动"
npx prisma studio --schema prisma/schema.prisma

echo "✅ Prisma Studio 已停止"
