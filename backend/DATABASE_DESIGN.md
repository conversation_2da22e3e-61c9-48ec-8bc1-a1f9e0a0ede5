# Yoghurt AI QC - 数据库设计文档

## 概述

本文档描述了 Yoghurt AI QC 系统的数据库设计，包括表结构、关系、索引和使用方法。

## 技术栈

- **数据库**: PostgreSQL 15
- **ORM**: Prisma
- **语言**: TypeScript
- **迁移工具**: 自定义迁移脚本

## 数据库架构

### 核心实体关系图

```mermaid
erDiagram
    User ||--o{ Recipe : creates
    User ||--o{ Batch : manages
    User ||--o{ SensoryAssessment : evaluates
    User ||--o{ MicroscopeImage : uploads
    User ||--o{ QualityReport : generates
    User ||--o{ SystemLog : performs
    
    Recipe ||--o{ Batch : uses
    Batch ||--o{ SensoryAssessment : has
    Batch ||--o{ MicroscopeImage : contains
    Batch ||--o{ AIAnalysis : analyzes
    Batch ||--o{ QualityReport : produces
    
    MicroscopeImage ||--o{ AIAnalysis : generates
    
    User {
        uuid id PK
        string email UK
        string password_hash
        string name
        enum role
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Recipe {
        uuid id PK
        uuid user_id FK
        string name
        text description
        jsonb ingredients
        jsonb process
        decimal fermentation_temperature
        integer fermentation_duration
        integer filtration_duration
        integer version
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    Batch {
        uuid id PK
        string batch_number UK
        uuid recipe_id FK
        uuid user_id FK
        enum status
        date production_date
        decimal quantity
        integer actual_fermentation_duration
        decimal actual_temperature
        text notes
        timestamp created_at
        timestamp updated_at
    }
```

## 表结构详细说明

### 1. 用户表 (users)

存储系统用户信息，支持角色管理。

**字段说明:**
- `id`: 主键，UUID类型
- `email`: 邮箱地址，唯一约束
- `password_hash`: 密码哈希值（bcrypt）
- `name`: 用户姓名
- `role`: 用户角色（ADMIN, USER, VIEWER）
- `is_active`: 账户状态

**索引:**
- `idx_users_email`: 邮箱索引
- `idx_users_role`: 角色索引
- `idx_users_is_active`: 活跃状态索引

### 2. 配方表 (recipes)

存储酸奶制作配方信息。

**字段说明:**
- `ingredients`: JSONB格式，存储配料信息
- `process`: JSONB格式，存储制作步骤
- `fermentation_temperature`: 发酵温度
- `fermentation_duration`: 发酵时长（小时）
- `filtration_duration`: 过滤时长（小时）

**JSON结构示例:**
```json
{
  "ingredients": [
    {"name": "全脂牛奶", "amount": 1000, "unit": "ml"},
    {"name": "酸奶菌粉", "amount": 1, "unit": "包"}
  ],
  "process": [
    {"step": 1, "description": "加热至85°C", "duration": 30, "temperature": 85}
  ]
}
```

### 3. 批次表 (batches)

记录每次酸奶生产的批次信息。

**状态枚举:**
- `PLANNING`: 计划中
- `IN_PROGRESS`: 进行中
- `FERMENTING`: 发酵中
- `FILTERING`: 过滤中
- `COMPLETED`: 已完成
- `FAILED`: 失败

### 4. 感官评估表 (sensory_assessments)

存储人工感官评估结果。

**评分字段:**
- `texture_score`: 质地评分（1-5）
- `acidity_score`: 酸度评分（1-5）
- `overall_score`: 综合评分（1-5）
- `flavor_tags`: JSONB格式的风味标签

### 5. 显微镜图像表 (microscope_images)

存储显微镜图像文件信息。

**字段说明:**
- `file_path`: 文件存储路径
- `magnification`: 放大倍数
- `staining_method`: 染色方法
- `metadata`: JSONB格式的额外元数据

### 6. AI分析表 (ai_analyses)

存储AI分析结果。

**分析状态:**
- `PENDING`: 待处理
- `PROCESSING`: 处理中
- `COMPLETED`: 已完成
- `FAILED`: 失败
- `CANCELLED`: 已取消

**分析结果字段:**
- `quality_score`: 质量评分（0-100）
- `bacterial_count`: 细菌计数
- `contamination_detected`: 是否检测到污染
- `morphology_analysis`: JSONB格式的形态学分析
- `confidence_score`: 置信度评分

### 7. 质量报告表 (quality_reports)

存储生成的质量报告。

**报告类型:**
- `QUALITY_SUMMARY`: 质量总结
- `BATCH_ANALYSIS`: 批次分析
- `TREND_ANALYSIS`: 趋势分析
- `CONTAMINATION_REPORT`: 污染报告
- `CUSTOM`: 自定义报告

### 8. 系统日志表 (system_logs)

记录系统操作日志。

## 数据库操作指南

### 初始化数据库

```bash
# 完整初始化（推荐）
npm run db:init

# 分步操作
npm run migrate        # 运行迁移
npm run db:seed        # 插入种子数据
npm run db:generate    # 生成Prisma客户端
```

### 迁移管理

```bash
# 查看迁移状态
npm run migrate:status

# 运行迁移
npm run migrate:up

# 回滚最后一个迁移（谨慎使用）
npm run migrate:rollback
```

### 种子数据管理

```bash
# 运行种子数据
npm run db:seed:run

# 清空数据库
npm run db:seed:clear

# 重置数据库（清空+种子数据）
npm run db:seed:reset

# 查看数据库状态
npm run db:seed:status

# 创建测试用户
npm run db:seed:test-users

# 创建示例数据
npm run db:seed:sample-data
```

### 数据库维护

```bash
# 检查数据库状态
npm run db:init:status

# 备份数据库
npm run db:backup

# 恢复数据库
npm run db:restore backup-file.sql

# 重置数据库
npm run db:init:reset
```

### Prisma Studio

```bash
# 打开数据库可视化界面
npm run db:studio
```

## 性能优化

### 索引策略

1. **主键索引**: 所有表都有UUID主键
2. **唯一索引**: email, batch_number等唯一字段
3. **外键索引**: 所有外键字段都有索引
4. **查询索引**: 常用查询字段的组合索引
5. **时间索引**: created_at, updated_at等时间字段

### 查询优化建议

1. 使用分页查询避免大量数据加载
2. 合理使用include减少N+1查询
3. 对JSONB字段使用GIN索引（如需要）
4. 定期分析查询性能

## 数据安全

### 密码安全
- 使用bcrypt加密，salt rounds = 12
- 密码不存储明文，仅存储哈希值

### 数据完整性
- 外键约束确保引用完整性
- 检查约束确保数据有效性
- 事务确保操作原子性

### 备份策略
- 定期自动备份
- 支持手动备份和恢复
- 生产环境建议每日备份

## 默认用户账户

系统初始化后会创建以下测试账户：

| 邮箱 | 密码 | 角色 | 说明 |
|------|------|------|------|
| <EMAIL> | password123 | ADMIN | 系统管理员 |
| <EMAIL> | password123 | USER | 普通用户 |
| <EMAIL> | password123 | USER | 咖啡厅用户 |
| <EMAIL> | password123 | VIEWER | 观察员 |

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库服务是否启动，端口是否正确
2. **迁移失败**: 检查数据库权限，确保用户有DDL权限
3. **种子数据失败**: 检查数据格式，确保没有重复数据

### 日志查看

```bash
# 查看应用日志
tail -f backend/logs/combined-*.log

# 查看错误日志
tail -f backend/logs/error-*.log
```

## 扩展建议

### 未来优化方向

1. **分区表**: 对大表（如system_logs）考虑按时间分区
2. **读写分离**: 高并发时考虑主从复制
3. **缓存层**: 添加Redis缓存热点数据
4. **全文搜索**: 使用PostgreSQL的全文搜索功能
5. **数据归档**: 定期归档历史数据
